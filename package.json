{"name": "rx-income-account", "version": "1.0.0", "description": "收益账户 based on Rax 1.0", "author": {"name": "桑陆", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "http://gitlab.alibaba-inc.com/trip/rx-income-account"}, "keywords": ["clam", "rx-income-account"], "lib": "rax", "type": "weex", "mobile": true, "universalTargets": {"web": true, "miniapp": false}, "clam2Version": "1.9.53", "group": "trip", "devDependencies": {"@types/ali-app": "^1.0.1", "@types/rax": "^1.0.0", "@typescript-eslint/eslint-plugin": "4.8.2", "@typescript-eslint/parser": "4.8.2", "typescript": "^4.0.3", "babel-eslint": "^8.2.5", "eslint": "^6.8.0", "eslint-plugin-babel": "^5.1.0", "eslint-plugin-jsx-plus": "^0.1.0", "eslint-plugin-react": "^7.10.0", "husky": "^8.0.3", "lint-staged": "^13.1.2", "prettier": "^2.8.4", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1"}, "dependencies": {"@ali/alitrip-base": "0.8.5", "@ali/rax-picture": "^3.4.3", "@ali/rxpi-env": "^1.0.0", "@ali/rxpi-hubble": "^1.9.1", "@ali/rxpi-loading": "^4.23.4", "@ali/rxpi-picture": "^4.2.1", "@ali/rxpi-result": "^4.23.4", "@ali/rxpi-toast": "^4.21.9", "@ali/rxpi-tracker-link": "^2.11.39", "@ali/rxpi-user-tracker": "^3.1.12", "@ali/rxpi-utils": "^1.0.0", "driver-universal": "^3.0.0", "jsx2mp-runtime": "^0.4.0", "rax": "^1.0.0", "rax-app": "^3.8.22", "rax-image": "^2.0.0", "rax-text": "^2.0.0", "rax-view": "^2.0.0", "universal-env": "^3.0.0"}, "overrides": {"parse-url": "^6.0.0"}, "clam": {"miniapp": {"compiler": "new"}, "inlineStyle": false, "useCdnCache": true, "minChunks": true}, "basePath": "pages/home/<USER>", "baseUrl": "/trip/rx-income-account/home/<USER>", "offlineTargets": {"trip": false, "taobao": false, "alipay": false}, "toolkit": "@ali/clam-toolkit-rx", "commandType": "clam", "lint-staged": {"*.{js,jsx,ts,tsx,md,html,css,less,scss}": "prettier --write"}, "scripts": {"prepare": "husky install"}, "defPublishType": "weex"}