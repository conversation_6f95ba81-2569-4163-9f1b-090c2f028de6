import { isAlipay } from '@ali/rxpi-env';

export const RIDS = ['1037527', '1037525', '1037528', '1037526', '1042009'];

// 账户卡片资源
export const ACCOUNT_CARD_SOURCE = {
  titleList: [
    'https://gw.alicdn.com/imgextra/i2/O1CN01BjpWVR26r4L08NLjy_!!*************-2-tps-334-90.png',
    isAlipay ? 'https://gw.alicdn.com/imgextra/i4/O1CN01cBWKSZ28FAnbyitHX_!!*************-2-tps-460-88.png' : 'https://gw.alicdn.com/imgextra/i3/O1CN01P8OvtN1yXMMql52BE_!!*************-2-tps-432-88.png',
  ]
};

// 明细卡片资源
export const LIST_CARD_SOURCE = {
  titleList: ['https://gw.alicdn.com/imgextra/i3/O1CN01O9zWhm1LIurYZ0095_!!*************-2-tps-334-90.png'],
  rightIcon: 'https://gw.alicdn.com/imgextra/i4/O1CN01uJVGJI1GuPCftUEsK_!!*************-2-tps-196-140.png',
};

export const TAB_BAR = [
  {
    title: '全部明细',
    value: 'allPoint',
    left: '8rpx',
  },
  {
    title: '入账明细',
    value: 'availablePoint',
    left: '135rpx',
  },
  {
    title: '使用明细',
    value: 'consumedPoint',
    left: '262rpx',
  },
  {
    title: '待使用券',
    value: 'coupon',
    left: '440rpx',
    showSplit: true,
    style: { fontWeight: 700 },
  },
];

export const CONFIG_RID = isAlipay ? 1099551 : 1093261;