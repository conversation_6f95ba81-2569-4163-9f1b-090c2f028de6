import { createElement } from 'rax';
import View from 'rax-view';
import TrackerLink from '@ali/rxpi-tracker-link';
import './index.less';

export default function IncomeItem(props) {
  const { name, couponValue, time, couponThreshold, jumpUrl } = props.item;

  return (
    <View className="coupon-item">
      <View className="coupon-item__detail">
        <View className="coupon-item__amount">
          <View className="coupon-item__unit">￥</View>
          {couponValue}
        </View>
        <View className="coupon-item__desc">{couponThreshold}</View>
      </View>
      <View className="coupon-item__info">
        <View className="coupon-item__title">{name}</View>
        <View className="coupon-item__time">{time}</View>
      </View>
      <TrackerLink
        data-need-exp
        data-spm="coupon"
        data-spm-click="gostr=/tbtrip;locaid=btn;"
        className="coupon-item__btn"
        href={jumpUrl}
      >
        去使用
      </TrackerLink>
    </View>
  );
}
