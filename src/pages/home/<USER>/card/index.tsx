import { createElement } from 'rax';
import View from 'rax-view';
import Picture from '@ali/rxpi-picture';
import './index.less';

function formatSource(uri?: string) {
  return { uri };
}

interface ICardProps {
  titleList: string[];
  rightIcon?: string;
  children?: any;
}

export default function Card(props: ICardProps) {
  const { titleList, rightIcon } = props;

  return (
    <View className="income-account__card">
      <View className="income-account__card-right">
        <Picture x-if={rightIcon} source={formatSource(rightIcon)} className="income-account__card-right-icon" />
        <slot name="icon"></slot>
      </View>
      <View className="income-account__card-content">
        <View className="income-account__card-header">
          {/* @ts-ignore */}
          <Picture x-for={(title, index) in titleList} key={index} style={{ height: '48rpx' }} mode="aspectFit" source={formatSource(title)} className="income-account__card-title" />
        </View>
        {props.children}
      </View>
    </View>
  );
}
