
import { getData } from '@ali/rxpi-utils';
import { RIDS, CONFIG_RID } from './constants';

export default async (ctx) => {
  // @ts-ignore
  if (window.__isSSR && ctx.env !== 'local') {
    try {
      const res = await ctx.hsfClient.invoke({
        id: 'com.fliggy.fcecore.service.CoreMgetService:1.0.0',
        group: 'HSF',
        method: 'getData',
        parameterTypes: ['com.fliggy.fcecore.domain.MultiMtopRequest'],
        args: [
          {
            rids: '822796',
          },
        ],
      });
      const title = getData(res, 'model.data.822796.data.title', '');
      const desc = getData(res, 'model.data.822796.data.desc', '');

      return {
        ssrData: {
          title,
          desc,
        },
      };
    } catch {
      return {
        ssrData: { 
          error: true,
        },
      };
    }
  }

  return {
    ssrData: { 
      error: true,
    },
  };
}
