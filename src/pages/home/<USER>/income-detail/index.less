.income-detail {
  &__tabs {
    margin-top: 24rpx;
    position: relative;
    display: flex;
    align-items: center;
    flex-direction: row;
    color: #666666;
    font-size: 26rpx;
    padding-bottom: 14rpx;
  }

  &__tab {
    margin: 0 12rpx;

    &:first-child {
      margin-left: 0;
    }

    &--active {
      color: #000000;
      font-weight: bold;
    }
  }

  &__split {
    margin: 0 24rpx;
    background: #999999;
    width: 2rpx;
    height: 26rpx;
  }

  &__indicator {
    position: absolute;
    bottom: 4rpx;
    width: 87rpx;
    height: 6rpx;
    border-radius: 3rpx;
    background: #FFE033;
    transition: left 0.5s;
  }

  &__coupons {
    margin-top: 12rpx;
  }

  &__hidden {
    display: none;
  }

  &__result {
    position: relative;
  }
}