import { createElement, Fragment, useEffect, useState, useRef, useMemo } from 'rax';
import View from 'rax-view';
import TrackerLink from '@ali/rxpi-tracker-link';
import Result from '@ali/rxpi-result';
import { getData } from '@ali/rxpi-utils';
import Card from '../card';
import IncomeItem from '../income-item';
import CouponItem from '../coupon-item';
import { getCouponList, getPointList } from '../../utils/api';
import { LIST_CARD_SOURCE, TAB_BAR } from '../../utils/constants';
import './index.less';

export default function SummaryCard(props) {
  const { activityId } = props;
  const defaultTab = TAB_BAR[0];
  const [currentTab, setCurrentTab] = useState(defaultTab.value);
  const [list, setList] = useState([]);
  const [left, setLeft] = useState(defaultTab.left);
  const [inited, setInited] = useState(false);
  const [couponList, setCouponList] = useState([]);
  const listRef = useRef({
    allPoint: [],
    availablePoint: [],
    consumedPoint: [],
  });

  const showResult = useMemo(() => {
    if (!inited) {
      return false;
    }
    if (currentTab === 'coupon') {
      return !couponList.length;
    }
    return !list.length;
  }, [list && list.length, couponList.length, inited, currentTab]);

  const onTabChange = (item) => {
    setLeft(item.left);
    setCurrentTab(item.value);
    setList(listRef.current[item.value] || []);
  };

  // 积分明细
  const fetchList = () => {
    return getPointList({ activityId})
      .then(res => {
        const { allPoint = [], availablePoint = [], consumedPoint = [] } = getData(res, 'data.model.pointDetail', {});
        listRef.current = {
          allPoint,
          availablePoint,
          consumedPoint,
        };
        setList(listRef.current[currentTab]);
      })
      .catch(err => {
        console.log(err);
      });
  };

  const fetchCoupon = () => {
    return getCouponList()
      .then(res => {
        setCouponList(getData(res, 'data.model.couponInfoList', []));
      })
      .catch(err => {
        console.log(err);
      });
  };

  useEffect(() => {
    Promise.all([
      fetchList(),
      fetchCoupon(),
    ]).then(() => {
      setInited(true);
    })
  }, []);

  return (
    <Card titleList={LIST_CARD_SOURCE.titleList} rightIcon={LIST_CARD_SOURCE.rightIcon}>
      <View className="income-detail__tabs">
        <Fragment x-for={(tab, index) in TAB_BAR} key={index}>
          <View x-if={tab.showSplit} className="income-detail__split" />
          <TrackerLink
            key={index}
            data-spm="tab"
            data-spm-click={`gostr=/tbtrip;locaid=${index};`}
            className={`income-detail__tab ${tab.value === currentTab ? 'income-detail__tab--active' : ''}`}
            onClick={() => onTabChange(tab)}
          >
            {tab.title}
          </TrackerLink>
        </Fragment>
        <View className="income-detail__indicator" style={{ left }} />
      </View>
      <View className="income-detail__list" x-class={{ 'income-detail__hidden': currentTab === 'coupon' }}>
        <IncomeItem
          x-for={(item, index) in list} 
          key={`${currentTab}-${index}`}
          item={item}
        />
      </View>
      <View className="income-detail__coupons"  x-class={{ 'income-detail__hidden': currentTab !== 'coupon' }}>
        <CouponItem
          x-for={(item, index) in couponList} 
          key={`${currentTab}-coupon-${index}`}
          item={item}
        />
      </View>
      <Result
        x-if={showResult}
        type="noGoods"
        text="暂无数据"
        button={false}
        style={{ position: 'relative' }}
      />
    </Card>
  )
}
