<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <title>收益明细</title>
    <meta name="aplus-terminal" content="1" />
    <meta name="aplus-waiting" content="MAN" />
    <meta name="aplus-spm-fixed" content="1" />
    <meta name="weex-viewport" content="750" />
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, viewport-fit=cover">
    <meta name="data-spm" content="181.********"/>
    <meta name="page-name" content="rx-income-account_home_index"/>
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="format-detection" content="telephone=no, email=no" />
    <link rel="dns-prefetch" href="//g.alicdn.com">
    <link rel="dns-prefetch" href="//gw.alicdn.com">
    <link rel="dns-prefetch" href="//log.mmstat.com">
    <link rel="dns-prefetch" href="//api.m.taobao.com">
    <link rel="dns-prefetch" href="//api.m.alitrip.com">
    <style>
      html, body {
        -ms-overflow-style: scrollbar;
        -webkit-tap-highlight-color: transparent;
        padding: 0;
        margin: 0;
        width: 100%;
        height: 100%;
        background-color: #f5f5f5;
      }

      body {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        font-family: BlinkMacSystemFont, 'Source Sans Pro', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        -webkit-user-select: none;
        user-select: none;
      }

      input[type="search"]::-webkit-search-decoration,
      input[type="search"]::-webkit-search-cancel-button {
        -webkit-appearance: none !important;
      }
      body [web-sticky] {
        position: -webkit-sticky !important;
        position: sticky !important;
      }
    </style>
    <!--#build<link rel="stylesheet" href="../../rx-income-account-commons.css"/>-->
    <!--#build<link rel="stylesheet" href="./index.css"/>-->
    <script>(function(global){if(global.define){return}var modules={};var inGuard=false;function def(id,deps,factory){if(deps instanceof Function){factory=deps;deps=[]}modules[id]={factory:factory,deps:deps,module:{exports:{}},isInitialized:false,hasError:false,}}function req(id){if(id.indexOf("@weex-module")===0){return{}}var originId=id;var mod=modules[id];if(!mod){id=id+"/index";mod=modules[id]}if(mod&&mod.isInitialized){return mod.module.exports}return requireImpl(id,originId)}function requireImpl(id,originId){if(global.ErrorUtils&&!inGuard){inGuard=true;var returnValue;try{returnValue=requireImpl.apply(this,arguments)}catch(e){global.ErrorUtils.reportFatalError(e)}inGuard=false;return returnValue}var mod=modules[id];if(!mod){throw new Error('Requiring unknown module "'+originId+'"')}if(mod.hasError){throw new Error('Requiring module "'+originId+'" which threw an exception')}try{mod.isInitialized=true;mod.factory(req,mod.module.exports,mod.module)}catch(e){mod.hasError=true;mod.isInitialized=false;throw e}return mod.module.exports}global.define=def;global.require=req})(this||typeof global==="object"&&global||typeof window==="object"&&window);</script>
    
  </head>
  <body data-noaplus="true">
    <!--__BEFORE_ROOT__-->
    <div id="root" key="root" style="z-index: 1;"><!--__INNER_ROOT__--></div>
    <!--__AFTER_ROOT__-->
    <script>
      if (window.performance && window.performance.timing && window.performance.timing.navigationStart) {
        window.__hb_perf_params = window.__hb_perf_params || {};
        if (window.__INITIAL_DATA__ && window.__INITIAL_DATA__.__SSR_ENABLED__) {
          window.__hb_perf_params.t2 = Date.now() - window.performance.timing.navigationStart;
        }
      }
    </script>

    <script src="../../../node_modules/@ali/alitrip-base/build/seed-weex-wv-min.js"></script>
    <script src="../../rx-income-account-commons.web.js"></script>
    <script src="./index.web.js"></script>
  </body>
</html>
