/** @jsx createElement */
import { createElement, useState, useEffect } from 'rax';
import { getData, useParams } from '@ali/rxpi-utils';
import Hubble from '@ali/rxpi-hubble';
import View from 'rax-view';
import { showToast } from '@ali/rxpi-toast';
import { showLoading, hideLoading } from '@ali/rxpi-loading';
import SummaryCard from './components/summary-card/index';
import IncomeDetail from './components/income-detail/index';
import { getAccountInfo, getConfig } from './utils/api';
import './index.css';

function App({ ssrData, location }) {
  // SSR数据
  const [accountInfo, setAccountInfo] = useState({});
  const [config, setConfig] = useState<Record<string, any>>({});
  const params = useParams(location);
  const { activityId } = params;

  const fetchDetail = () => {
    showLoading();
    Promise.all([
      getAccountInfo({ activityId }),
      getConfig(),
    ]).then(([accountRes, configRes]) => {
      hideLoading();
      const configData = getData(configRes, 'data.data') || {};
      const formatedConfig = Object.keys(configData).reduce((acc, key) => {
        const { sname, data } = configData[key]
        return {
          ...acc,
          [sname]: data,
        };
      }, {});
      setConfig(formatedConfig);
      setAccountInfo(getData(accountRes, 'data.model.personalAssetDto', {}));
    }).catch(([err]) => {
      hideLoading();
      showToast(getData(err, 'data.displayMessage') || '活动火爆中');
    });
  };

  useEffect(() => {
    // 参考：https://yuque.antfin.com/fliggy-perf/docs/collect
    Hubble.init('181.********', {
      pageName: '收益账户',
      router: 'pages/home/<USER>',
      sampling: 1,
      pid: 'haa3j9plpi@f4f051a21054dc1', // TODO: 换成本项目对应的pid
    });

    fetchDetail();
  }, []);

  return (
    <View className="income-account">
      <SummaryCard data={accountInfo} config={config.incomeConfig} />
      <IncomeDetail activityId={activityId} />
    </View>
  );
}

App.getInitialProps = async (ctx) => {
  if (window.__isSSR && ctx.env !== 'local') {
    try {
      const res = await ctx.hsfClient.invoke({
        id: 'com.fliggy.fcecore.service.CoreMgetService:1.0.0',
        group: 'HSF',
        method: 'getData',
        parameterTypes: ['com.fliggy.fcecore.domain.MultiMtopRequest'],
        args: [
          {
            rids: '822796',
          },
        ],
      });
      const title = getData(res, 'model.data.822796.data.title', '');
      const desc = getData(res, 'model.data.822796.data.desc', '');

      return {
        ssrData: {
          title,
          desc,
        },
      };
    } catch {
      return {
        ssrData: { 
          error: true,
        },
      };
    }
  }

  return {
    ssrData: { 
      error: true,
    },
  };
}

export default App;