import { createElement, Fragment, useMemo } from 'rax';
import View from 'rax-view';
import TrackerLink from '@ali/rxpi-tracker-link';
import Card from '../card';
import { ACCOUNT_CARD_SOURCE } from '../../utils/constants';
import './index.less';

export default function SummaryCard(props) {
  const { estimatePoint = 0, obtainedPoint = 0, accumulativePointNumber } = props.data;
  const { ruleUrl, exchangeWithdrawUrl } = props.config || {};

  const moreAmount = useMemo(() => {
    return [
      {
        title: '出行后预计可获得(元)',
        amount: estimatePoint,
      },
      {
        title: '当前账户累计收益(元)',
        amount: accumulativePointNumber,
      },
    ];
  }, [estimatePoint, accumulativePointNumber]);

  return (
    <Card titleList={ACCOUNT_CARD_SOURCE.titleList}>
      <TrackerLink
        x-slot:icon
        data-need-exp
        data-spm="card"
        data-spm-click="gostr=/tbtrip;locaid=explanation;"
        className="summary-card__icon"
        href={ruleUrl}
      >
        <View className="summary-card__icon-text">说明</View>
      </TrackerLink>
      <View className="summary-card__account">
        <View>
          <View className="summary-card__account-title">当前账户可使用(元)</View>
          <View className="summary-card__account-amount">
            <View className="summary-card__unit">￥</View>
            {obtainedPoint}
          </View>
        </View>
        <TrackerLink
          data-need-exp
          data-spm="card"
          data-spm-click="gostr=/tbtrip;locaid=btn;"
          className="summary-card__account-btn"
          href={exchangeWithdrawUrl}
        >
          兑换无门槛优惠券/提现
        </TrackerLink>
      </View>
      <View className="summary-card__detail">
        <View
          x-for={(item, index) in moreAmount}
          key={index}
          className="summary-card__detail-item"
        >
          <View className="summary-card__detail-title">{item.title}</View>
          <View className="summary-card__detail-amount">
            <View className="summary-card__unit">￥</View>
            {item.amount}
          </View>
        </View>
      </View>
    </Card>
  )
}
