import Hubble from '@ali/rxpi-hubble';
import { isAlipay } from '@ali/rxpi-env';
import { RIDS, CONFIG_RID } from './constants';

export function getConfig() {
  return Hubble.request({
    api: 'mtop.trip.fcecore.api.wx.mget',
    v: '1.0',
    data: { rids: RIDS.join(',') },
  }).catch(err => {
    console.log('获取泰坦配置失败', err);
  });
}

export function getAccountInfo(data) {
  return Hubble.request({
    api: 'mtop.trip.cash.activity.wx',
    v: '1.0',
    data: {
      asac: '7A24C27O3K62F3O4UUPDFZ',
      clientType: isAlipay ? 2 : 1,
      ...data,
    },
  });
};

/** 查询积分明细 */
export function getPointList(data) {
  return Hubble.request({
    api: 'mtop.trip.cash.point.list',
    v: '1.0',
    data: {
      clientType: isAlipay ? 2 : 1,
      ...data,
    },
  });
}

/** 未使用券列表 */
export function getCouponList() {
  return Hubble.request({
    api: 'mtop.trip.cash.activity.coupon.get.wx',
    v: '1.0',
    data: {
      rid: CONFIG_RID,
    },
  });
}
