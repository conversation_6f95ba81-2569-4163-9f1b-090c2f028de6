.income-item {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  padding: 24rpx 0;
  font-size: 26rpx;
  border-bottom: 1rpx solid #D2D4D9;

  &:last-child {
    border: none;
  }

  &__title {
    height: 36rpx;
    font-weight: 500;
  }

  &__time {
    margin-top: 12rpx;
    color: #5C5F66;
    height: 37rpx;
  }

  &__value {
    font-size: 28rpx;
    line-height: 40rpx;
    color: #18B928;

    &--positive {
      color: #FF3333;
    }
  }
}