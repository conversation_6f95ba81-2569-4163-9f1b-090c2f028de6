import { createElement, useMemo } from 'rax';
import View from 'rax-view';
import './index.less';

export default function IncomeItem(props) {
  const { operateMemo, gmtModified, operateDetail } = props.item;

  const valueClassName = useMemo(() => {
    return operateDetail.indexOf('+') > -1 ? ' income-item__value--positive' : '';
  }, [operateDetail]);

  return (
    <View className="income-item">
      <View className="income-item__detail">
        <View className="income-item__title">{operateMemo}</View>
        <View className="income-item__time">{gmtModified}</View>
      </View>
      <View className={`income-item__value${valueClassName}`}>{operateDetail}</View>
    </View>
  );
}
