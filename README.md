## rx-income-account

> 收益账户

- 项目类型: Rax 1.0 项目
- 创建者: 桑陆 <<EMAIL>>
- 项目地址: http://gitlab.alibaba-inc.com/trip/rx-income-account

### 初始化

1. `<NAME_EMAIL>:trip/rx-income-account.git` 克隆仓库
2. `tnpm install` 安装依赖 `npm` 包
    
### clam 任务

       commit  本地提交代码
         push  远程提交代码
       prepub  clam 预发                                                 
      publish  clam 正式发布   
          dev  开启 Dev 开发模式
  dev:miniapp  开启小程序 Dev 开发模式
     dev home  调试单个或多个页面
        build  默认构建流程                                         
  build:cloud  默认构建流程                                           
   build:trip  构建飞猪客户端离线包                                              
 build:taobao  构建手淘客户端离线包                                              
 build:alipay  构建支付宝钱包客户端离线包                                           
build:offline  构建离线包目标容器选择处理
        debug  调试离线包代码                        
     download  下载云构建结果
     add page  新增页面
      add mod  新增模块

### changeLog

TODO
