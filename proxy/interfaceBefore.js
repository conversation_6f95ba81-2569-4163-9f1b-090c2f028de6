/**
 * Created by 桑陆<<EMAIL>> on 2025-8-28.
 * Proxy For Interface Mock Before
 */
"use strict";

var url = require('url');

/**
 * 接口 mock 处理模块（发送请求前，直接返回数据）
 * @param requestUrl {String} 请求 URL
 * @returns {Object} 返回 response 对象
 */
module.exports = function (requestUrl) {
  var parsedReqUrl = url.parse(requestUrl, true);
  var params = parsedReqUrl.query;
  var data;

  switch (params.api) {
    case 'mtop.trip.test.interfaceBefore':
      data = {
        test: 123
      };
      break;
  }

  if (data) {
    return {
      api: params.api,
      v: params.v || '1.0',
      ret: ["SUCCESS::调用成功"],
      data
    };
  }

  // 不拦截返回 undefined
};
