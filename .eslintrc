{"env": {"es6": true, "node": true, "mocha": true}, "globals": {"my": true, "wx": true, "App": true, "Page": true, "weex": true, "Component": true, "document": true, "navigator": true, "location": true, "Rax": true, "__VERSION__": true, "__ONLINE__": true, "__OFFLINE_TRIP__": true, "__OFFLINE_TAOBAO__": true, "__OFFLINE_ALIPAY__": true, "__CURRENT_GRUNT_TASK__": true}, "rules": {"semi": "off", "react/react-in-jsx-scope": "off"}, "root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 9, "sourceType": "module"}, "extends": ["plugin:jsx-plus/recommended", "plugin:prettier/recommended"], "plugins": ["jsx-plus", "@typescript-eslint"]}