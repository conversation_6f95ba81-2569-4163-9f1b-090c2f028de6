{"compileOnSave": false, "buildOnSave": false, "compilerOptions": {"baseUrl": ".", "outDir": "build", "module": "esnext", "target": "es6", "jsx": "preserve", "jsxFactory": "createElement", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "lib": ["es6", "dom"], "sourceMap": true, "allowJs": false, "rootDir": "./", "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": false, "importHelpers": true, "strictNullChecks": true, "suppressImplicitAnyIndexErrors": true, "noUnusedLocals": true, "skipLibCheck": true, "paths": {"@/*": ["./src/*"], "rax-app": [".rax/index.ts"]}}, "include": ["src"], "exclude": ["node_modules", "build", "build+ssr", "public"]}